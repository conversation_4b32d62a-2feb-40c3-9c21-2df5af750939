#!/usr/bin/env python3
"""
PDF Data Extractor and Professional Report Generator
Author: <PERSON><PERSON> Project
Purpose: Extract data from existing PDF and create professional report
"""

import PyPDF2
import re
from typing import Dict, List, Any
import json

class PDFDataExtractor:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.extracted_data = {}
        self.raw_text = ""
    
    def extract_text_from_pdf(self) -> str:
        """Extract all text content from PDF"""
        try:
            with open(self.pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text() + "\n"
                
                self.raw_text = text
                return text
        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
            return ""
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize extracted text"""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove the specific date mentioned
        text = re.sub(r'10/02/2025', '', text)
        text = re.sub(r'10-02-2025', '', text)
        text = re.sub(r'February 10, 2025', '', text)
        return text.strip()
    
    def parse_data_sections(self, text: str) -> Dict[str, Any]:
        """Parse text into structured data sections"""
        data = {
            'title': '',
            'sections': [],
            'tables': [],
            'key_points': [],
            'numerical_data': []
        }
        
        # Extract title (usually first significant line)
        lines = text.split('\n')
        for line in lines:
            if line.strip() and len(line.strip()) > 10:
                data['title'] = line.strip()
                break
        
        # Extract numerical data
        numbers = re.findall(r'\d+\.?\d*', text)
        data['numerical_data'] = [float(n) for n in numbers if n]
        
        # Extract sections based on common patterns
        section_pattern = r'([A-Z][A-Za-z\s]+):\s*([^:]+?)(?=[A-Z][A-Za-z\s]+:|$)'
        sections = re.findall(section_pattern, text)
        data['sections'] = [{'title': s[0].strip(), 'content': s[1].strip()} for s in sections]
        
        return data
    
    def extract_and_structure_data(self) -> Dict[str, Any]:
        """Main method to extract and structure all data"""
        print("Extracting text from PDF...")
        raw_text = self.extract_text_from_pdf()
        
        print("Cleaning and processing text...")
        cleaned_text = self.clean_text(raw_text)
        
        print("Parsing data sections...")
        structured_data = self.parse_data_sections(cleaned_text)
        
        self.extracted_data = structured_data
        return structured_data
    
    def save_extracted_data(self, output_file: str = "extracted_data.json"):
        """Save extracted data to JSON file for review"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.extracted_data, f, indent=2, ensure_ascii=False)
        print(f"Extracted data saved to {output_file}")

if __name__ == "__main__":
    # Extract data from the existing PDF
    extractor = PDFDataExtractor("PR for Balsamand MCAD Project.pdf")
    data = extractor.extract_and_structure_data()
    extractor.save_extracted_data()
    
    print("Data extraction completed!")
    print(f"Title: {data.get('title', 'N/A')}")
    print(f"Number of sections: {len(data.get('sections', []))}")
    print(f"Numerical data points: {len(data.get('numerical_data', []))}")
