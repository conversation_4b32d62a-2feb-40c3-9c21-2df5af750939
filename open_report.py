#!/usr/bin/env python3
"""
Quick script to open the generated professional PDF report
Author: Moond <PERSON>
"""

import os
import subprocess
import sys

def open_pdf():
    """Open the professional PDF report"""
    pdf_file = "Professional_Balsamand_MCAD_Report.pdf"
    
    if not os.path.exists(pdf_file):
        print(f"Error: {pdf_file} not found!")
        print("Please run create_professional_report.py first.")
        return False
    
    try:
        # Open PDF with default system application
        if sys.platform.startswith('win'):
            os.startfile(pdf_file)
        elif sys.platform.startswith('darwin'):  # macOS
            subprocess.run(['open', pdf_file])
        else:  # Linux
            subprocess.run(['xdg-open', pdf_file])
        
        print(f"Opening {pdf_file}...")
        return True
    except Exception as e:
        print(f"Error opening PDF: {e}")
        return False

if __name__ == "__main__":
    print("Professional PDF Report Viewer")
    print("=" * 40)
    
    if open_pdf():
        print("PDF opened successfully!")
    else:
        print("Failed to open PDF.")
    
    input("Press Enter to exit...")
