#!/usr/bin/env python3
"""
Professional PDF Report Generator
Author: Moon<PERSON> Project
Purpose: Generate professional PDF reports with modern design
"""

from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY
from reportlab.pdfgen import canvas
from datetime import datetime
import json
from typing import Dict, List, Any

class ProfessionalPDFGenerator:
    def __init__(self, output_filename: str = "Professional_Balsamand_Report.pdf"):
        self.output_filename = output_filename
        self.doc = SimpleDocTemplate(
            output_filename,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )
        self.styles = getSampleStyleSheet()
        self.story = []
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Create custom styles for professional appearance"""
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2C3E50'),
            fontName='Helvetica-Bold'
        ))
        
        # Heading style
        self.styles.add(ParagraphStyle(
            name='CustomHeading',
            parent=self.styles['Heading1'],
            fontSize=16,
            spaceAfter=12,
            spaceBefore=20,
            textColor=colors.HexColor('#34495E'),
            fontName='Helvetica-Bold'
        ))
        
        # Subheading style
        self.styles.add(ParagraphStyle(
            name='CustomSubHeading',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=10,
            spaceBefore=15,
            textColor=colors.HexColor('#7F8C8D'),
            fontName='Helvetica-Bold'
        ))
        
        # Body text style
        self.styles.add(ParagraphStyle(
            name='CustomBody',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=6,
            alignment=TA_JUSTIFY,
            textColor=colors.HexColor('#2C3E50'),
            fontName='Helvetica'
        ))
        
        # Executive summary style
        self.styles.add(ParagraphStyle(
            name='ExecutiveSummary',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=8,
            alignment=TA_JUSTIFY,
            textColor=colors.HexColor('#2C3E50'),
            fontName='Helvetica',
            leftIndent=20,
            rightIndent=20,
            borderWidth=1,
            borderColor=colors.HexColor('#BDC3C7'),
            borderPadding=10
        ))
    
    def add_header_footer(self, canvas, doc):
        """Add professional header and footer"""
        canvas.saveState()
        
        # Header
        canvas.setFont('Helvetica-Bold', 10)
        canvas.setFillColor(colors.HexColor('#34495E'))
        canvas.drawString(72, A4[1] - 50, "BALSAMAND MCAD PROJECT REPORT")
        
        # Footer
        canvas.setFont('Helvetica', 9)
        canvas.setFillColor(colors.HexColor('#7F8C8D'))
        canvas.drawRightString(A4[0] - 72, 50, f"Page {doc.page}")
        canvas.drawString(72, 50, f"Generated: {datetime.now().strftime('%B %Y')}")
        
        canvas.restoreState()
    
    def create_title_page(self, title: str):
        """Create professional title page"""
        # Main title
        self.story.append(Spacer(1, 2*inch))
        self.story.append(Paragraph(title, self.styles['CustomTitle']))
        self.story.append(Spacer(1, 0.5*inch))
        
        # Subtitle
        subtitle = "Comprehensive Project Analysis and Report"
        self.story.append(Paragraph(subtitle, self.styles['CustomSubHeading']))
        self.story.append(Spacer(1, 1*inch))
        
        # Project details box
        project_info = [
            ["Project Type:", "MCAD Engineering Project"],
            ["Report Type:", "Professional Analysis"],
            ["Status:", "Completed"],
            ["Generated:", datetime.now().strftime('%B %Y')]
        ]
        
        table = Table(project_info, colWidths=[2*inch, 3*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#ECF0F1')),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.HexColor('#2C3E50')),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#BDC3C7')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 12),
            ('RIGHTPADDING', (0, 0), (-1, -1), 12),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        self.story.append(table)
        self.story.append(PageBreak())
    
    def create_executive_summary(self, data: Dict[str, Any]):
        """Create executive summary section"""
        self.story.append(Paragraph("Executive Summary", self.styles['CustomHeading']))
        
        summary_text = """
        This comprehensive report presents a detailed analysis of the Balsamand MCAD project, 
        encompassing all critical aspects of the engineering design and implementation process. 
        The project demonstrates advanced mechanical computer-aided design principles and 
        showcases innovative solutions to complex engineering challenges.
        
        Key achievements include successful completion of design objectives, optimization of 
        mechanical systems, and implementation of industry-standard CAD practices. The project 
        results indicate strong performance metrics and adherence to engineering specifications.
        """
        
        self.story.append(Paragraph(summary_text, self.styles['ExecutiveSummary']))
        self.story.append(Spacer(1, 20))
    
    def create_data_section(self, sections: List[Dict[str, str]]):
        """Create sections from extracted data"""
        for section in sections:
            if section.get('title') and section.get('content'):
                self.story.append(Paragraph(section['title'], self.styles['CustomHeading']))
                self.story.append(Paragraph(section['content'], self.styles['CustomBody']))
                self.story.append(Spacer(1, 12))
    
    def create_professional_table(self, data: List[List[str]], headers: List[str]):
        """Create professionally styled table"""
        table_data = [headers] + data
        
        table = Table(table_data, repeatRows=1)
        table.setStyle(TableStyle([
            # Header styling
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#34495E')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            
            # Body styling
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#ECF0F1')),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.HexColor('#2C3E50')),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            
            # Grid and padding
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#BDC3C7')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        return table
    
    def generate_report(self, data: Dict[str, Any]):
        """Generate the complete professional report"""
        print("Generating professional PDF report...")
        
        # Title page
        title = data.get('title', 'Balsamand MCAD Project Report')
        self.create_title_page(title)
        
        # Executive summary
        self.create_executive_summary(data)
        
        # Main content sections
        if data.get('sections'):
            self.create_data_section(data['sections'])
        
        # Build PDF
        self.doc.build(self.story, onFirstPage=self.add_header_footer, 
                      onLaterPages=self.add_header_footer)
        
        print(f"Professional PDF report generated: {self.output_filename}")

def load_extracted_data(filename: str = "extracted_data.json") -> Dict[str, Any]:
    """Load extracted data from JSON file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Data file {filename} not found. Using default data.")
        return {
            'title': 'Balsamand MCAD Project Report',
            'sections': [],
            'tables': [],
            'numerical_data': []
        }

if __name__ == "__main__":
    # Load extracted data
    data = load_extracted_data()
    
    # Generate professional PDF
    generator = ProfessionalPDFGenerator()
    generator.generate_report(data)
    
    print("Professional PDF generation completed!")
